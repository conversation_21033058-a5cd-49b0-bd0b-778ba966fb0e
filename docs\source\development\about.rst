About
=====

About Manim
-----------

Manim is an animation engine for explanatory math videos. 
You can use it to make math videos (or other fields) like 3Blue1Brown.

There are mainly two versions here:

- `3b1b/manim <https://github.com/3b1b/manim>`_ : Maintained by <PERSON> of 3Blue1Brown.

Using OpenGL and its GLSL language to use GPU for rendering. It has higher efficiency, 
faster rendering speed, and supports real-time rendering and interaction.

- `ManimCommunity/manim <https://github.com/ManimCommunity/manim>`_ : Maintained by Manim Community Dev Team.

Using multiple backend rendering. There is better documentation and 
a more open contribution community.

About this documentation
------------------------

This documentation is based on the version in `3b1b/manim <https://github.com/3b1b/manim>`_. 
Created by `<PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>`_ ("鹤翔万里" in Chinese) and in production.

Among them, the ``manim_example_ext`` extension for <PERSON>phinx refers to 
`the documentation of ManimCommunity <https://docs.manim.community/>`_.

If you want to contribute to manim or this document, please see: :doc:`contributing`