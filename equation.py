from manim import *
import numpy as np

# ====== 可調整參數配置 ======
class AnimationConfig:
    # 時間參數
    SCENE_DURATION = 4.0        # 每個場景持續時間
    TRANSITION_TIME = 1.0       # 過渡時間
    HIGHLIGHT_DURATION = 2.0    # 突出顯示時間
    FAST_ANIM = 0.8            # 快速動畫
    NORMAL_ANIM = 1.5          # 標準動畫
    SLOW_ANIM = 2.5            # 慢速動畫
    PAUSE_TIME = 1.5           # 暫停時間
    
    # 顏色參數
    PRIMARY_COLOR = WHITE       # 主要內容顏色
    HIGHLIGHT_COLOR = YELLOW    # 突出顯示顏色
    RESULT_COLOR = GREEN        # 結果顯示顏色
    PROCESS_COLOR = BLUE        # 過程顯示顏色
    ERROR_COLOR = RED           # 錯誤提醒顏色
    
    # 位置參數
    TITLE_POSITION = UP * 3.2     # 標題位置
    EQUATION_POSITION = UP * 1.5  # 方程式位置
    WORK_POSITION = ORIGIN        # 計算過程位置
    EXPLANATION_POSITION = DOWN * 2.5  # 說明文字位置
    RESULT_POSITION = DOWN * 3.2   # 結果位置

# ====== 配置設置 ======
config.pixel_height = 720
config.pixel_width = 1280
config.frame_rate = 30

class LinearSystemSolution(Scene):
    def construct(self):
        # 設置中文字體
        Text.set_default(font="SimHei", font_size=32)
        
        # ====== 場景1：問題呈現 (8秒) ======
        self.show_problem()
        
        # ====== 場景2：解題策略說明 (6秒) ======
        self.explain_strategy()
        
        # ====== 場景3：步驟1 - 分析方程組 (5秒) ======
        self.step1_analyze()
        
        # ====== 場景4：步驟2 - 消元法求解x (8秒) ======
        self.step2_elimination()
        
        # ====== 場景5：步驟3 - 代入求解y (6秒) ======
        self.step3_substitution()
        
        # ====== 場景6：步驟4 - 驗證解 (6秒) ======
        self.step4_verification()
        
        # ====== 場景7：幾何意義展示 (10秒) ======
        self.step5_geometric_meaning()
        
        # ====== 場景8：總結 (5秒) ======
        self.conclusion()

    def show_problem(self):
        """場景1：展示完整題目和關鍵資訊"""
        # 標題
        title = Text("線性方程組求解", color=AnimationConfig.HIGHLIGHT_COLOR, font_size=48)
        title.move_to(AnimationConfig.TITLE_POSITION)
        
        # 問題描述
        problem_text = Text("求解下列線性方程組：", font_size=36)
        problem_text.move_to(UP * 2)
        
        # 方程組
        equation1 = MathTex(r"x - y = 5", font_size=44)
        equation2 = MathTex(r"x + 2y = 11", font_size=44)
        equation1.move_to(UP * 0.5)
        equation2.move_to(DOWN * 0.5)
        
        # 編號
        eq1_label = MathTex(r"(1)", font_size=36).next_to(equation1, LEFT)
        eq2_label = MathTex(r"(2)", font_size=36).next_to(equation2, LEFT)
        
        # 突出顯示框
        eq_group = VGroup(equation1, equation2, eq1_label, eq2_label)
        highlight_box = SurroundingRectangle(eq_group, color=AnimationConfig.HIGHLIGHT_COLOR, buff=0.3)
        
        # 動畫展示
        self.play(Write(title), run_time=AnimationConfig.FAST_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME)
        
        self.play(Write(problem_text), run_time=AnimationConfig.FAST_ANIM)
        self.wait(AnimationConfig.FAST_ANIM)
        
        self.play(
            Write(equation1),
            Write(eq1_label),
            run_time=AnimationConfig.NORMAL_ANIM
        )
        self.play(
            Write(equation2),
            Write(eq2_label),
            run_time=AnimationConfig.NORMAL_ANIM
        )
        
        self.play(Create(highlight_box), run_time=AnimationConfig.FAST_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME)
        
        # 保存重要元素
        self.equation1 = equation1
        self.equation2 = equation2
        self.eq1_label = eq1_label
        self.eq2_label = eq2_label
        
        # 清除場景
        self.play(
            FadeOut(title),
            FadeOut(problem_text),
            FadeOut(highlight_box),
            run_time=AnimationConfig.FAST_ANIM
        )

    def explain_strategy(self):
        """場景2：解題策略說明"""
        strategy_title = Text("解題策略：消元法", color=AnimationConfig.PROCESS_COLOR, font_size=40)
        strategy_title.move_to(AnimationConfig.TITLE_POSITION)
        
        strategy_steps = VGroup(
            Text("1. 選擇消去變數（選擇 y）", font_size=28),
            Text("2. 將方程(1)減去方程(2)消去 x", font_size=28),
            Text("3. 求出 y 的值", font_size=28),
            Text("4. 代入原方程求出 x", font_size=28),
            Text("5. 驗證解的正確性", font_size=28)
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.4)
        strategy_steps.move_to(ORIGIN)
        
        self.play(Write(strategy_title), run_time=AnimationConfig.FAST_ANIM)
        self.wait(AnimationConfig.FAST_ANIM)
        
        for step in strategy_steps:
            self.play(Write(step), run_time=AnimationConfig.FAST_ANIM)
            self.wait(0.3)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(FadeOut(strategy_title), FadeOut(strategy_steps), run_time=AnimationConfig.FAST_ANIM)

    def step1_analyze(self):
        """步驟1：分析方程組結構"""
        step_title = Text("步驟1：分析方程組", color=AnimationConfig.PROCESS_COLOR, font_size=36)
        step_title.move_to(AnimationConfig.TITLE_POSITION)
        
        # 重新顯示方程組
        eq1 = self.equation1.copy().move_to(UP * 1)
        eq2 = self.equation2.copy().move_to(ORIGIN)
        eq1_label = self.eq1_label.copy().next_to(eq1, LEFT)
        eq2_label = self.eq2_label.copy().next_to(eq2, LEFT)
        
        analysis = Text("觀察：x的係數相同，適合用減法消元", 
                       color=AnimationConfig.HIGHLIGHT_COLOR, font_size=28)
        analysis.move_to(DOWN * 1.5)
        
        self.play(Write(step_title), run_time=AnimationConfig.FAST_ANIM)
        self.play(
            Write(eq1), Write(eq1_label),
            Write(eq2), Write(eq2_label),
            run_time=AnimationConfig.NORMAL_ANIM
        )
        
        # 突出顯示x的係數
        x_coeff1 = eq1[0][0]  # x in equation 1
        x_coeff2 = eq2[0][0]  # x in equation 2
        
        self.play(
            Indicate(x_coeff1, color=AnimationConfig.HIGHLIGHT_COLOR),
            Indicate(x_coeff2, color=AnimationConfig.HIGHLIGHT_COLOR),
            run_time=AnimationConfig.NORMAL_ANIM
        )
        
        self.play(Write(analysis), run_time=AnimationConfig.NORMAL_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME)
        
        self.play(FadeOut(step_title), FadeOut(analysis), run_time=AnimationConfig.FAST_ANIM)
        
        # 保存方程組位置
        self.current_eq1 = eq1
        self.current_eq2 = eq2
        self.current_eq1_label = eq1_label
        self.current_eq2_label = eq2_label

    def step2_elimination(self):
        """步驟2：消元法求解"""
        step_title = Text("步驟2：方程(1) - 方程(2) 消去 x", color=AnimationConfig.PROCESS_COLOR, font_size=36)
        step_title.move_to(AnimationConfig.TITLE_POSITION)
        
        self.play(Write(step_title), run_time=AnimationConfig.FAST_ANIM)
        
        # 顯示減法過程
        subtraction = MathTex(r"(x - y) - (x + 2y) = 5 - 11", font_size=40)
        subtraction.move_to(DOWN * 0.5)
        
        self.play(Write(subtraction), run_time=AnimationConfig.NORMAL_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME)
        
        # 展開計算
        expanded = MathTex(r"x - y - x - 2y = -6", font_size=40)
        expanded.move_to(DOWN * 1.5)
        
        self.play(Transform(subtraction.copy(), expanded), run_time=AnimationConfig.NORMAL_ANIM)
        self.play(Write(expanded), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 合併同類項
        simplified = MathTex(r"-3y = -6", font_size=44, color=AnimationConfig.RESULT_COLOR)
        simplified.move_to(DOWN * 2.5)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(Write(simplified), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 求解y
        y_solution = MathTex(r"y = 2", font_size=48, color=AnimationConfig.RESULT_COLOR)
        y_solution.move_to(DOWN * 3.2)
        
        solve_arrow = Arrow(simplified.get_bottom(), y_solution.get_top(), 
                           color=AnimationConfig.HIGHLIGHT_COLOR, buff=0.1)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(GrowArrow(solve_arrow), run_time=AnimationConfig.FAST_ANIM)
        self.play(Write(y_solution), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 突出顯示結果
        result_box = SurroundingRectangle(y_solution, color=AnimationConfig.RESULT_COLOR, buff=0.2)
        self.play(Create(result_box), run_time=AnimationConfig.FAST_ANIM)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        
        # 保存y的值
        self.y_value = y_solution
        
        # 清除中間步驟
        self.play(
            FadeOut(step_title),
            FadeOut(subtraction),
            FadeOut(expanded),
            FadeOut(simplified),
            FadeOut(solve_arrow),
            FadeOut(result_box),
            run_time=AnimationConfig.FAST_ANIM
        )

    def step3_substitution(self):
        """步驟3：代入求解x"""
        step_title = Text("步驟3：將 y = 2 代入方程(1)求 x", color=AnimationConfig.PROCESS_COLOR, font_size=36)
        step_title.move_to(AnimationConfig.TITLE_POSITION)
        
        self.play(Write(step_title), run_time=AnimationConfig.FAST_ANIM)
        
        # 顯示代入過程
        substitution = MathTex(r"x - 2 = 5", font_size=44)
        substitution.move_to(UP * 0.5)
        
        self.play(Write(substitution), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 移項
        move_term = Text("移項：", color=AnimationConfig.PROCESS_COLOR, font_size=28)
        move_term.move_to(LEFT * 3 + DOWN * 0.5)
        
        x_solution_eq = MathTex(r"x = 5 + 2", font_size=44)
        x_solution_eq.move_to(DOWN * 0.5)
        
        self.play(Write(move_term), run_time=AnimationConfig.FAST_ANIM)
        self.play(Write(x_solution_eq), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 最終結果
        x_solution = MathTex(r"x = 7", font_size=48, color=AnimationConfig.RESULT_COLOR)
        x_solution.move_to(DOWN * 1.8)
        
        solve_arrow = Arrow(x_solution_eq.get_bottom(), x_solution.get_top(), 
                           color=AnimationConfig.HIGHLIGHT_COLOR, buff=0.1)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(GrowArrow(solve_arrow), run_time=AnimationConfig.FAST_ANIM)
        self.play(Write(x_solution), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 顯示完整解
        complete_solution = VGroup(
            x_solution.copy(),
            self.y_value.copy().move_to(DOWN * 2.5)
        )
        
        solution_box = SurroundingRectangle(complete_solution, color=AnimationConfig.RESULT_COLOR, buff=0.3)
        solution_label = Text("方程組的解：", color=AnimationConfig.RESULT_COLOR, font_size=32)
        solution_label.next_to(solution_box, UP, buff=0.2)
        
        self.play(
            Create(solution_box),
            Write(solution_label),
            Write(complete_solution[1]),
            run_time=AnimationConfig.NORMAL_ANIM
        )
        
        self.wait(AnimationConfig.PAUSE_TIME)
        
        # 保存解
        self.x_value = x_solution
        self.solution_group = VGroup(complete_solution, solution_box, solution_label)
        
        # 清除工作步驟
        self.play(
            FadeOut(step_title),
            FadeOut(substitution),
            FadeOut(move_term),
            FadeOut(x_solution_eq),
            FadeOut(solve_arrow),
            run_time=AnimationConfig.FAST_ANIM
        )

    def step4_verification(self):
        """步驟4：驗證解的正確性"""
        step_title = Text("步驟4：驗證解 x = 7, y = 2", color=AnimationConfig.PROCESS_COLOR, font_size=36)
        step_title.move_to(AnimationConfig.TITLE_POSITION)
        
        self.play(Write(step_title), run_time=AnimationConfig.FAST_ANIM)
        
        # 驗證第一個方程
        verify1_text = Text("驗證方程(1)：", font_size=28)
        verify1_text.move_to(UP * 1.5 + LEFT * 4)
        
        verify1 = MathTex(r"7 - 2 = 5 \quad ✓", font_size=36, color=AnimationConfig.RESULT_COLOR)
        verify1.move_to(UP * 1.5)
        
        # 驗證第二個方程
        verify2_text = Text("驗證方程(2)：", font_size=28)
        verify2_text.move_to(UP * 0.5 + LEFT * 4)
        
        verify2 = MathTex(r"7 + 2(2) = 7 + 4 = 11 \quad ✓", font_size=36, color=AnimationConfig.RESULT_COLOR)
        verify2.move_to(UP * 0.5)
        
        self.play(Write(verify1_text), Write(verify1), run_time=AnimationConfig.NORMAL_ANIM)
        self.wait(AnimationConfig.FAST_ANIM)
        self.play(Write(verify2_text), Write(verify2), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 結論
        conclusion = Text("✓ 驗證通過，解正確！", color=AnimationConfig.RESULT_COLOR, font_size=32)
        conclusion.move_to(DOWN * 0.5)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(Write(conclusion), run_time=AnimationConfig.NORMAL_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME)
        
        self.play(
            FadeOut(step_title),
            FadeOut(verify1_text), FadeOut(verify1),
            FadeOut(verify2_text), FadeOut(verify2),
            FadeOut(conclusion),
            run_time=AnimationConfig.FAST_ANIM
        )

    def step5_geometric_meaning(self):
        """步驟5：幾何意義展示"""
        step_title = Text("步驟5：幾何意義 - 兩直線的交點", color=AnimationConfig.PROCESS_COLOR, font_size=36)
        step_title.move_to(UP * 3.5)
        
        self.play(Write(step_title), run_time=AnimationConfig.FAST_ANIM)
        
        # 創建坐標系
        axes = Axes(
            x_range=[-2, 12, 2],
            y_range=[-2, 8, 2],
            x_length=6,
            y_length=4,
            axis_config={"color": BLUE, "include_ticks": True},
        ).move_to(DOWN * 0.5)
        
        # 坐標軸標籤
        axes_labels = axes.get_axis_labels(x_label="x", y_label="y")
        
        self.play(Create(axes), Write(axes_labels), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 繪製第一條直線 x - y = 5 → y = x - 5
        line1 = axes.plot(lambda x: x - 5, x_range=[3, 11], color=RED)
        line1_label = MathTex(r"x - y = 5", font_size=24, color=RED)
        line1_label.move_to(axes.coords_to_point(9, 2))
        
        # 繪製第二條直線 x + 2y = 11 → y = (11 - x) / 2
        line2 = axes.plot(lambda x: (11 - x) / 2, x_range=[1, 11], color=BLUE)
        line2_label = MathTex(r"x + 2y = 11", font_size=24, color=BLUE)
        line2_label.move_to(axes.coords_to_point(3, 4))
        
        self.play(Create(line1), Write(line1_label), run_time=AnimationConfig.NORMAL_ANIM)
        self.play(Create(line2), Write(line2_label), run_time=AnimationConfig.NORMAL_ANIM)
        
        # 標記交點
        intersection_point = axes.coords_to_point(7, 2)
        point_dot = Dot(intersection_point, color=AnimationConfig.RESULT_COLOR, radius=0.08)
        point_label = MathTex(r"(7, 2)", font_size=28, color=AnimationConfig.RESULT_COLOR)
        point_label.next_to(point_dot, UR, buff=0.1)
        
        self.wait(AnimationConfig.PAUSE_TIME)
        self.play(GrowFromCenter(point_dot), run_time=AnimationConfig.FAST_ANIM)
        self.play(Write(point_label), run_time=AnimationConfig.FAST_ANIM)
        
        # 突出顯示交點
        highlight_circle = Circle(radius=0.3, color=AnimationConfig.HIGHLIGHT_COLOR)
        highlight_circle.move_to(intersection_point)
        self.play(Create(highlight_circle), run_time=AnimationConfig.FAST_ANIM)
        self.play(FadeOut(highlight_circle), run_time=AnimationConfig.FAST_ANIM)
        
        explanation = Text("交點 (7, 2) 就是方程組的唯一解", 
                          color=AnimationConfig.RESULT_COLOR, font_size=28)
        explanation.move_to(DOWN * 3)
        
        self.play(Write(explanation), run_time=AnimationConfig.NORMAL_ANIM)
        self.wait(AnimationConfig.PAUSE_TIME * 2)
        
        self.play(
            FadeOut(step_title), FadeOut(axes), FadeOut(axes_labels),
            FadeOut(line1), FadeOut(line1_label),
            FadeOut(line2), FadeOut(line2_label),
            FadeOut(point_dot), FadeOut(point_label),
            FadeOut(explanation),
            run_time=AnimationConfig.NORMAL_ANIM
        )

    def conclusion(self):
        """總結場景"""
        title = Text("解題總結", color=AnimationConfig.HIGHLIGHT_COLOR, font_size=48)
        title.move_to(UP * 2.5)
        
        # 最終答案
        final_answer = VGroup(
            Text("線性方程組", font_size=32),
            MathTex(r"\begin{cases} x - y = 5 \\ x + 2y = 11 \end{cases}", font_size=36),
            Text("的解為：", font_size=32),
            MathTex(r"x = 7, \quad y = 2", font_size=40, color=AnimationConfig.RESULT_COLOR)
        ).arrange(DOWN, buff=0.3)
        final_answer.move_to(ORIGIN)
        
        # 方法總結
        method_summary = Text("解法：消元法（減法消元）", 
                             color=AnimationConfig.PROCESS_COLOR, font_size=28)
        method_summary.move_to(DOWN * 2.5)
        
        self.play(Write(title), run_time=AnimationConfig.FAST_ANIM)
        self.wait(AnimationConfig.FAST_ANIM)
        
        for item in final_answer:
            self.play(Write(item), run_time=AnimationConfig.FAST_ANIM)
            self.wait(0.3)
        
        self.play(Write(method_summary), run_time=AnimationConfig.FAST_ANIM)
        
        # 最終突出顯示
        final_box = SurroundingRectangle(final_answer, color=AnimationConfig.RESULT_COLOR, buff=0.3)
        self.play(Create(final_box), run_time=AnimationConfig.FAST_ANIM)
        
        self.wait(AnimationConfig.PAUSE_TIME * 2)