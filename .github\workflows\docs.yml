name: docs

on: 
  push:
    paths:
      - 'docs/**'
  pull_request:
    paths:
      - 'docs/**'

jobs:
  docs:
    runs-on: ubuntu-latest
    name: build up document and deploy

    steps:
    - name: Checkout
      uses: actions/checkout@master
    
    - name: Install sphinx and manim env
      run: |
        pip3 install --upgrade pip
        sudo apt install python3-setuptools libpango1.0-dev
        pip3 install -r docs/requirements.txt
        pip3 install -r requirements.txt
    
    - name: Build document with Sphinx
      run: |
        cd docs
        export PATH="$PATH:/home/<USER>/.local/bin"
        export SPHINXBUILD="python3 -m sphinx"
        make html
        
    - name: Deploy to GitHub pages
      if: ${{ github.event_name == 'push' }}
      uses: JamesIves/github-pages-deploy-action@3.7.1
      with:
        ACCESS_TOKEN: ${{ secrets.DOC_DEPLOY_TOKEN }}
        BRANCH: gh-pages
        FOLDER: docs/build/html
