[metadata]
name = manimgl
version = 1.7.2
author = <PERSON>
author_email= <EMAIL>
description = Animation engine for explanatory math videos
long_description = file: README.md
long_description_content_type = text/markdown; charset=UTF-8
home_page = https://github.com/3b1b/manim
project_urls =
    Bug Tracker = https://github.com/3b1b/manim/issues
    Documentation = https://3b1b.github.io/manim/
    Source Code = https://github.com/3b1b/manim
license = MIT
classifiers =
    Development Status :: 4 - Beta
    License :: OSI Approved :: MIT License
    Topic :: Scientific/Engineering
    Topic :: Multimedia :: Video
    Topic :: Multimedia :: Graphics
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3 :: Only
    Natural Language :: English

[options]
packages = find:
include_package_data = True
install_requires =
    addict
    appdirs
    audioop-lts; python_version >= "3.13"
    colour
    diskcache
    ipython>=8.18.0
    isosurfaces
    fontTools
    manimpango>=0.6.0
    mapbox-earcut
    matplotlib
    moderngl
    moderngl_window
    numpy
    Pillow
    pydub
    pygments
    PyOpenGL
    pyperclip
    pyyaml
    rich
    scipy
    screeninfo
    setuptools
    skia-pathops
    svgelements>=1.8.1
    sympy
    tqdm
    typing-extensions; python_version < "3.11"
    validators

[options.entry_points]
console_scripts =
    manimgl = manimlib.__main__:main
    manim-render = manimlib.__main__:main
