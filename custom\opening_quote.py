from manimlib.animation.creation import Write
from manimlib.animation.fading import FadeIn
from manimlib.constants import *
from manimlib.mobject.svg.tex_mobject import TexText
from manimlib.scene.scene import Scene
from manimlib.utils.rate_functions import linear


class OpeningQuote(Scene):
    quote = []
    quote_arg_separator = " "
    highlighted_quote_terms = {}
    author = ""
    fade_in_kwargs = {
        "lag_ratio": 0.5,
        "rate_func": linear,
        "run_time": 5,
    }
    text_size = R"\Large"
    use_quotation_marks = True
    top_buff = 1.0
    author_buff = 1.0

    def construct(self):
        self.quote = self.get_quote()
        self.author = self.get_author(self.quote)

        self.play(FadeIn(self.quote, **self.fade_in_kwargs))
        self.wait(2)
        self.play(Write(self.author, run_time=3))
        self.wait()

    def get_quote(self, max_width=FRAME_WIDTH - 1):
        text_mobject_kwargs = {
            "alignment": "",
            "arg_separator": self.quote_arg_separator,
        }
        if isinstance(self.quote, str):
            if self.use_quotation_marks:
                quote = OldTexText("``%s''" %
                                    self.quote.strip(), **text_mobject_kwargs)
            else:
                quote = OldTexText("%s" %
                                    self.quote.strip(), **text_mobject_kwargs)
        else:
            if self.use_quotation_marks:
                words = [self.text_size + " ``"] + list(self.quote) + ["''"]
            else:
                words = [self.text_size] + list(self.quote)
            quote = OldTexText(*words, **text_mobject_kwargs)
            # TODO, make less hacky
            if self.quote_arg_separator == " ":
                quote[0].shift(0.2 * RIGHT)
                quote[-1].shift(0.2 * LEFT)
        for term, color in self.highlighted_quote_terms:
            quote.set_color_by_tex(term, color)
        quote.to_edge(UP, buff=self.top_buff)
        if quote.get_width() > max_width:
            quote.set_width(max_width)
        return quote

    def get_author(self, quote):
        author = OldTexText(self.text_size + " --" + self.author)
        author.next_to(quote, DOWN, buff=self.author_buff)
        author.set_color(YELLOW)
        return author
