Installation
============

Manim runs on Python 3.7 or higher.

System requirements are：

- `FFmpeg <https://ffmpeg.org/>`__
- `OpenGL <https://www.opengl.org//>`__ (included in python package ``PyOpenGL``)
- `LaTeX <https://www.latex-project.org>`__ (optional, if you want to use LaTeX)
- `Pango <https://pango.org>`__ (only for Linux)


Install FFmpeg
--------------



Install FFmpeg Windows
------------------------
.. code-block:: cmd

   choco install ffmpeg


# Install FFmepeg Linux
----------------------------
.. code-block:: sh

   $ sudo apt update
   $ sudo apt install ffmpeg
   $ ffmpeg -version
  
# Install FFmpeg MacOS
----------------------------
- Download This ZIP file `https://www.gyan.dev/ffmpeg/builds/ffmpeg-git-full.7z`(if the link is not working download this zip file from there original website)



Directly
--------

.. code-block:: sh

   # Install manimgl
   pip install manimgl

   # Try it out
   manimgl

If you want to hack on manimlib itself, clone this repository and in
that directory execute:

.. code-block:: sh

   # Install python requirements
   pip install -e .

   # Try it out
   manimgl example_scenes.py OpeningManimExample
   # or
   manim-render example_scenes.py OpeningManimExample

If you run the above command and no error message appears, 
then you have successfully installed all the environments required by manim.

Directly (Windows)
------------------

1. `Install
   FFmpeg <https://www.wikihow.com/Install-FFmpeg-on-Windows>`__, and make sure that its path is in the PATH environment variable.
2. Install a LaTeX distribution.
   `TeXLive-full <http://tug.org/texlive/>`__ is recommended.
3. Install the remaining Python packages.

.. code-block:: sh  

   git clone https://github.com/3b1b/manim.git
   cd manim  
   pip install -e . 
   manimgl example_scenes.py OpeningManimExample

For Anaconda
------------

-  Install FFmpeg and LaTeX as above.
-  Create a conda environment using

.. code-block:: sh
   
   git clone https://github.com/3b1b/manim.git
   cd manim 
   conda create -n manim python=3.8
   conda activate manim
   pip install -e .
