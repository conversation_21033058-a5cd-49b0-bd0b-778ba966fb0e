# This file determines the default configuration for how manim is
# run, including names for directories it will write to, default
# parameters for various classes, style choices, etc. To customize
# your own, create a custom_config.yml file in whatever directory
# you are running manim. For 3blue1brown, for instance, mind is
# here: https://github.com/3b1b/videos/blob/master/custom_config.yml

# Alternatively, you can create it wherever you like, and on running
# manim, pass in `--config_file /path/to/custom/config/file.yml`

directories:
  # Set this to true if you want the path to video files
  # to match the directory structure of the path to the
  # source code generating that video
  mirror_module_path: False
  # Manim may write to and read from the file system, e.g.
  # to render videos and to look for svg/png assets. This
  # will specify where those assets live, with a base directory,
  # and various subdirectory names within it
  base: ""
  subdirs:
    # Where should manim output video and image files?
    output: "videos"
    # If you want to use images, manim will look to these folders to find them
    raster_images: "raster_images"
    vector_images: "vector_images"
    # If you want to use sounds, manim will look here to find it.
    sounds: "sounds"
    # Place for other forms of data relevant to any projects, like csv's
    data: "data"
    # When downloading, say an image, where will it go?
    downloads: "downloads"
  # For certain object types, especially Tex and Text, manim will save information
  # to file to prevent the need to re-compute, e.g. recompiling the latex. By default,
  # it stores this saved data to whatever directory appdirs.user_cache_dir("manim") returns,
  # but here a user can specify a different cache location
  cache: ""
window:
  # The position of window on screen. UR -> Upper Right, and likewise DL -> Down and Left,
  # UO would be upper middle, etc.
  position_string: UR
  # If using multiple monitors, which one should show the window
  monitor_index: 0
  # If not full screen, the default to give it half the screen width
  full_screen: False
  # Other optional specifications that override the above include:
  # position: (500, 500)  # Specific position, in pixel coordinates, for upper right corner
  # size: (1920, 1080)  # Specific size, in pixels
camera:
  resolution: (1920, 1080)
  background_color: "#333333"
  fps: 30
  background_opacity: 1.0
file_writer:
  # What command to use for ffmpeg
  ffmpeg_bin: "ffmpeg"
  # Parameters to pass into ffmpeg
  video_codec: "libx264"
  pixel_format: "yuv420p"
  saturation: 1.0
  gamma: 1.0
# Most of the scene configuration will come from CLI arguments,
# but defaults can be set here
scene:
  show_animation_progress: False
  leave_progress_bars: False
  # When skipping animations, should a single frame be rendered
  # at the end of each play call?
  preview_while_skipping: True
  # How long does a scene pause on Scene.wait calls
  default_wait_time: 1.0
vmobject:
  default_stroke_width: 4.0
  default_stroke_color: "#DDDDDD"     # Default is GREY_A
  default_fill_color: "#888888"       # Default is GREY_C
mobject:
  default_mobject_color: "#FFFFFF"    # Default is WHITE
  default_light_color: "#BBBBBB"      # Default is GREY_B
tex:
  # See tex_templates.yml
  template: "default"
text:
  # font: "Cambria Math"
  font: "Consolas"
  alignment: "LEFT"
embed:
  exception_mode: "Verbose"
  autoreload: False
resolution_options:
  # When the user passes in -l, -m, --hd or --uhd, these are the corresponding
  # resolutions
  low: (854, 480)
  med: (1280, 720)
  high: (1920, 1080)
  4k: (3840, 2160)
sizes:
  # This determines the scale of the manim coordinate system with respect to
  # the viewing frame
  frame_height: 8.0
  # These determine the constants SMALL_BUFF, MED_SMALL_BUFF, etc., useful
  # for nudging things around and having default spacing values
  small_buff: 0.1
  med_small_buff: 0.25
  med_large_buff: 0.5
  large_buff: 1.0
  # Default buffers used in Mobject.next_to or Mobject.to_edge
  default_mobject_to_edge_buff: 0.5
  default_mobject_to_mobject_buff: 0.25
key_bindings:
  pan_3d: "d"
  pan: "f"
  reset: "r"
  quit: "q" # Together with command
  select: "s"
  unselect: "u"
  grab: "g"
  x_grab: "h"
  y_grab: "v"
  resize: "t"
  color: "c"
  information: "i"
  cursor: "k"
colors:
  blue_e: "#1C758A"
  blue_d: "#29ABCA"
  blue_c: "#58C4DD"
  blue_b: "#9CDCEB"
  blue_a: "#C7E9F1"
  teal_e: "#49A88F"
  teal_d: "#55C1A7"
  teal_c: "#5CD0B3"
  teal_b: "#76DDC0"
  teal_a: "#ACEAD7"
  green_e: "#699C52"
  green_d: "#77B05D"
  green_c: "#83C167"
  green_b: "#A6CF8C"
  green_a: "#C9E2AE"
  yellow_e: "#E8C11C"
  yellow_d: "#F4D345"
  yellow_c: "#FFFF00"
  yellow_b: "#FFEA94"
  yellow_a: "#FFF1B6"
  gold_e: "#C78D46"
  gold_d: "#E1A158"
  gold_c: "#F0AC5F"
  gold_b: "#F9B775"
  gold_a: "#F7C797"
  red_e: "#CF5044"
  red_d: "#E65A4C"
  red_c: "#FC6255"
  red_b: "#FF8080"
  red_a: "#F7A1A3"
  maroon_e: "#94424F"
  maroon_d: "#A24D61"
  maroon_c: "#C55F73"
  maroon_b: "#EC92AB"
  maroon_a: "#ECABC1"
  purple_e: "#644172"
  purple_d: "#715582"
  purple_c: "#9A72AC"
  purple_b: "#B189C6"
  purple_a: "#CAA3E8"
  grey_e: "#222222"
  grey_d: "#444444"
  grey_c: "#888888"
  grey_b: "#BBBBBB"
  grey_a: "#DDDDDD"
  white: "#FFFFFF"
  black: "#000000"
  grey_brown: "#736357"
  dark_brown: "#8B4513"
  light_brown: "#CD853F"
  pink: "#D147BD"
  light_pink: "#DC75CD"
  green_screen: "#00FF00"
  orange: "#FF862F"
  pure_red: "#FF0000"
  pure_green: "#00FF00"
  pure_blue: "#0000FF"
# Can be DEBUG / INFO / WARNING / ERROR / CRITICAL
log_level: "INFO"
universal_import_line: "from manimlib import *"
ignore_manimlib_modules_on_reload: True
