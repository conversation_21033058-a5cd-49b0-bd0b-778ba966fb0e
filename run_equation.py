#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>rip<PERSON> to run the equation animation with proper UTF-8 encoding.
"""

import os
import sys
import subprocess
from pathlib import Path

def set_utf8_environment():
    """Set environment variables for UTF-8 encoding."""
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    # For Windows
    os.environ['CHCP'] = '65001'

def run_equation_scene(scene_name="LinearSystemSolution", output_format="mp4"):
    """Run the equation scene with proper encoding."""
    
    # Set UTF-8 environment
    set_utf8_environment()
    
    # Activate virtual environment and run
    if output_format == "mp4":
        cmd = [
            "manimgl", "equation.py", scene_name,
            "-w",  # Write to file
            "--file_name", f"{scene_name}_output"
        ]
    else:
        cmd = [
            "manimgl", "equation.py", scene_name,
            "-s"  # Save last frame
        ]
    
    # Set environment variables
    env = os.environ.copy()
    env["PYTHONPATH"] = "."
    env["PYTHONIOENCODING"] = "utf-8"
    env["PYTHONUTF8"] = "1"
    
    print(f"🎬 Running {scene_name} with UTF-8 encoding...")
    print(f"📁 Output format: {output_format}")
    
    try:
        # Change to Windows command for activation
        if sys.platform == "win32":
            activation_cmd = "source .venv/Scripts/activate && " + " ".join(cmd)
            result = subprocess.run(
                activation_cmd,
                shell=True,
                cwd=".",
                env=env,
                text=True,
                encoding='utf-8'
            )
        else:
            result = subprocess.run(
                cmd,
                cwd=".",
                env=env,
                text=True,
                encoding='utf-8'
            )
        
        if result.returncode == 0:
            print(f"✅ Successfully rendered {scene_name}")
            return True
        else:
            print(f"❌ Failed to render {scene_name}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def main():
    """Main function."""
    print("🎥 Starting equation animation with UTF-8 support")
    
    # Try different approaches
    approaches = [
        ("mp4", "Video output"),
        ("png", "Image output")
    ]
    
    for output_format, description in approaches:
        print(f"\n📋 Trying {description}...")
        if run_equation_scene(output_format=output_format):
            print(f"✅ Success with {description}")
            break
        else:
            print(f"❌ Failed with {description}")
    
    print("\n🎉 Done!")

if __name__ == "__main__":
    main()
